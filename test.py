import os
from groq import Groq

client = Groq(
    # api_key=("********************************************************"), # my key
    api_key=("********************************************************"),
)



# chat_completion = client.chat.completions.create(
#     messages=[
#         {
#             "role": "user",
#             "content": "what is the job url of mooser & partner AG",
#         }
#     ],
#     model="openai/gpt-oss-120b",
# )
#
# print(chat_completion.choices[0].message.content)

# from openai import OpenAI
#
# # Initialize the client with your API key
# client_openai = OpenAI(api_key="********************************************************************************************************************************************************************")
#
# # Make a chat completion request
# response = client_openai.chat.completions.create(
#     model="gpt-4o-mini",  # You can use "gpt-4o-mini", "gpt-4.1", etc.
#     messages=[
#         {"role": "system", "content": "You are a helpful assistant."},
#         {"role": "user", "content": "what is the job url of mooser & partner AG"}
#     ]
# )
#
# # Print the response
# print(response.choices[0].message.content)

from jobsitescraper.log_manager import CustomLogger
import scrapy, re
import traceback, sys
from jobsitescraper.utils import env
from scrapy.exceptions import CloseSpider
from bs4 import BeautifulSoup
from datetime import datetime, timezone

class mooserpartnerChSpider(scrapy.Spider):
    name = 'mooserpartner.ch'
    close_down = False
    config = {}
    i = 0
    page_num = 1
    total = 0
    count = -1
    isLive = env('PRODUCTION')

    def __init__(self, _config=None, **kwargs):
        super().__init__(**kwargs)
        if self.isLive == 'True':
            self.config = _config
        else:
            self.config = self.get_config()

    def get_config(self):
        return {
            'SourceKey': 'mooserpartner_ch',
            'BaseUrl': 'https://www.mooserpartner.ch',
            'StartUrl': 'https://www.mooserpartner.ch/fuer-privatpersonen/job-liste.htm',
            'SourceCountry': 'ch',
            'LangCode': 'de',
            'Upload': True,
            'IsActive': True,
            'Custom': True,
            'MaxPagesToCrawl': 10,
            'MaxJobsToCrawl': 500,
            'RecentJobs': True,
            'DeleteAllJobsOnStart': True
        }

    def start_requests(self):
        try:
            CustomLogger.LogEvent(self.config['SourceKey'], 'Spider started')
            if self.config.get('RecentJobs'):
                yield scrapy.Request(
                    url=self.config['StartUrl'],
                    callback=self.parse_recent,
                    errback=self.handle_error,
                    dont_filter=True
                )
        except Exception as e:
            CustomLogger.LogEvent(self.config['SourceKey'], str(e))
            CustomLogger.LogEvent(self.config['SourceKey'], traceback.format_exc())

    def handle_error(self, failure):
        CustomLogger.LogEvent(self.config['SourceKey'], str(failure))
        CustomLogger.LogEvent(self.config['SourceKey'], traceback.format_exc())

    def parse_recent(self, response):
        try:
            website_text = response.body.decode('utf-8')
            soup = BeautifulSoup(website_text.replace('<', ' <'), 'html.parser')
            job_links = []
            for a in soup.find_all('a', href=True):
                href = a['href']
                if '/job-liste/' in href and href.endswith('.htm'):
                    job_links.append(response.urljoin(href))
            for link in set(job_links):
                yield scrapy.Request(
                    url=link,
                    callback=self.parse_job,
                    errback=self.handle_error,
                    dont_filter=True
                )
            if self.page_num < self.config.get('MaxPagesToCrawl', 10):
                next_page = soup.find('a', string=re.compile(r'weiter|next', re.I))
                if next_page and next_page.get('href'):
                    self.page_num += 1
                    yield scrapy.Request(
                        url=response.urljoin(next_page['href']),
                        callback=self.parse_recent,
                        errback=self.handle_error,
                        dont_filter=True
                    )
        except Exception as e:
            CustomLogger.LogEvent(self.config['SourceKey'], str(e))
            CustomLogger.LogEvent(self.config['SourceKey'], traceback.format_exc())

    def parse_job(self, response):
        try:
            if self.close_down:
                raise CloseSpider('took down by analyzer')
            website_text = response.body.decode('utf-8')
            soup = BeautifulSoup(website_text.replace('<', ' <'), 'html.parser')
            # Title
            title_tag = soup.find('h1')
            jobTitle = title_tag.get_text(strip=True) if title_tag else ''
            # Location
            loc_span = soup.find('span', class_=re.compile(r'icon[-_]icon[-_]location'))
            jobLocation = ''
            if loc_span and loc_span.parent:
                jobLocation = loc_span.parent.get_text(strip=True)
            jobLocation = re.sub(r'[\xa0]', ' ', jobLocation).strip()
            if not jobLocation:
                jobLocation = 'Switzerland'
            # Company Name
            company_name = ''
            meta_name = soup.find('meta', {'property': 'og:site_name'})
            if meta_name and meta_name.get('content'):
                company_name = meta_name['content'].strip()
            if not company_name:
                footer_p = soup.find('div', class_='footer')
                if footer_p:
                    txt = footer_p.get_text(separator=' ', strip=True)
                    match = re.search(r'Mooser\s*&\s*Partner\s*AG', txt, re.I)
                    if match:
                        company_name = match.group(0)
            if not company_name:
                company_name = 'Mooser & Partner AG'
            # Company Logo
            logo_tag = soup.find('img', class_=re.compile(r'logo'))
            if logo_tag and logo_tag.get('src'):
                logo_url = logo_tag['src']
                if logo_url.startswith('http'):
                    CompanyLogoFileURL = logo_url
                else:
                    CompanyLogoFileURL = response.urljoin(logo_url)
            else:
                CompanyLogoFileURL = self.config['BaseUrl'] + '/includes/images/logo.svg'
            # Raw content
            RawContent = str(soup)
            # Clean content
            for tag_name in ['a', 'img', 'svg', 'script', 'style', 'footer', 'textarea', 'button', 'path', 'head']:
                for tag in soup.find_all(tag_name):
                    tag.decompose()
            clean_text = soup.get_text(separator=' ', strip=True)
            clean_text = re.sub(r'\s+', ' ', clean_text)
            CleanContent = clean_text
            # Contact info
            emails = re.findall(r'\S+@\S+', clean_text)
            phones = re.findall(r'[\+\(]?[1-9][0-9 \-\(\)]{8,}[0-9]', clean_text.replace('\u00a0', ' '))
            JobContactEmails = ', '.join(set(emails))
            JobContactPhone = ', '.join(set(phones))
            # Timestamps
            now_iso = datetime.now(timezone.utc).astimezone().isoformat()
            # UID
            SourceUID = re.sub(r'[^a-zA-Z0-9]', '', response.url)
            ad = {
                'JobTitle': jobTitle,
                'JobLocation': jobLocation,
                'CompanyLogoFileURL': CompanyLogoFileURL,
                'CompanyName': company_name,
                'SourceURL': response.url,
                'SourceCountry': self.config['SourceCountry'],
                'SourceKey': self.config['SourceKey'],
                'SourceLangCode': self.config['LangCode'],
                'CrawlTimestamp': now_iso,
                'SourceUID': SourceUID,
                'CleanContent': CleanContent,
                'RawContent': RawContent,
                'PostedDate': now_iso,
                'JobContactEmails': JobContactEmails,
                'JobContactPhone': JobContactPhone
            }
            self.count += 1
            if self.count >= self.config.get('MaxJobsToCrawl', 500):
                self.close_down = True
                raise CloseSpider('max jobs reached')
            yield ad
        except Exception as e:
            CustomLogger.LogEvent(self.config['SourceKey'], str(e))
            CustomLogger.LogEvent(self.config['SourceKey'], traceback.format_exc())

    def close(self, reason):
        try:
            CustomLogger.LogEvent(self.config['SourceKey'], f'Crawler Stopped, Total Jobs: {self.count}')
        except Exception as e:
            CustomLogger.LogEvent(self.config['SourceKey'], str(e))
            CustomLogger.LogEvent(self.config['SourceKey'], traceback.format_exc())